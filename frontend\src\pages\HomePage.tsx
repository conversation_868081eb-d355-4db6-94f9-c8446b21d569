import React, { useState, useEffect, useCallback } from 'react';
import { Input, Space, Button, Typography, Statistic, Row, Col, AutoComplete } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import HomePageMap from '../components/Map/HomePageMap';

const { Title } = Typography;

const HomePage: React.FC = () => {
  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<{ value: string }[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [mapData, setMapData] = useState({
    totalSpecies: 1247,
    totalRecordings: 8936,
    activeHotspots: 342,
    onlineUsers: 156
  });

  // 检测移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 处理搜索输入变化
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    
    if (value) {
      // 模拟搜索建议
      const mockSuggestions = [
        { value: '座头鲸 (Megaptera novaeangliae)' },
        { value: '蓝鲸 (Balaenoptera musculus)' },
        { value: '虎鲸 (Orcinus orca)' },
        { value: '太平洋' },
        { value: '大西洋' },
      ].filter(item => 
        item.value.toLowerCase().includes(value.toLowerCase())
      );
      
      setSearchOptions(mockSuggestions);
    } else {
      setSearchOptions([]);
    }
  }, []);

  // 处理搜索
  const handleSearch = useCallback(async (value: string) => {
    if (!value.trim()) return;
    
    setIsLoading(true);
    
    try {
      // 模拟搜索API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('搜索:', value);
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 处理地图数据加载
  const handleDataLoad = useCallback((data: any) => {
    setMapData(prev => ({ ...prev, ...data }));
  }, []);

  return (
    <div style={{
      height: 'calc(100vh - 80px)', // 减去header高度
      display: 'flex',
      flexDirection: 'column',
      background: '#f8f9fa',
      position: 'relative'
    }}>
      {/* 顶部搜索栏 - 类似eBird */}
      <div style={{
        background: 'white',
        borderBottom: '1px solid #e5e7eb',
        padding: '12px 16px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        zIndex: 1000
      }}>
        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        }}>
          <div style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#1e3a8a',
            minWidth: '120px',
            fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
          }}>
            观鸟热点
          </div>

          <div style={{ flex: 1, maxWidth: '400px' }}>
            <AutoComplete
              options={searchOptions}
              value={searchValue}
              onChange={handleSearchChange}
              onSelect={handleSearch}
              style={{ width: '100%' }}
            >
              <Input
                placeholder="输入地点名称或物种..."
                allowClear
                prefix={<SearchOutlined style={{ color: '#6b7280' }} />}
                style={{
                  borderRadius: '6px',
                  border: '1px solid #d1d5db'
                }}
              />
            </AutoComplete>
          </div>

          <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
            <Button size="small" style={{ borderRadius: '4px' }}>
              日期
            </Button>
            <Button size="small" style={{ borderRadius: '4px' }}>
              全年，所有物种
            </Button>
            <Button size="small" style={{ borderRadius: '4px' }}>
              热点
            </Button>
            <Button
              type="primary"
              size="small"
              icon={<SearchOutlined />}
              style={{
                borderRadius: '4px',
                background: '#1e3a8a',
                borderColor: '#1e3a8a'
              }}
            >
              输入地点名称或地址
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 - 地图占满屏幕 */}
      <div style={{
        flex: 1,
        display: 'flex',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 地图区域 - 占据大部分空间 */}
        <div style={{
          flex: 1,
          position: 'relative',
          background: 'white'
        }}>
          <HomePageMap
            height="100%"
            onDataLoad={handleDataLoad}
          />
        </div>

        {/* 右侧边栏 - 类似eBird的侧边栏 */}
        {!isMobile && (
          <div style={{
            width: '320px',
            background: 'white',
            borderLeft: '1px solid #e5e7eb',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            {/* 侧边栏标题 */}
            <div style={{
              padding: '16px',
              borderBottom: '1px solid #e5e7eb',
              background: '#f8f9fa'
            }}>
              <Title level={4} style={{
                margin: 0,
                color: '#1f2937',
                fontSize: '16px',
                fontWeight: '600',
                fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
              }}>
                观察数据统计
              </Title>
            </div>

            {/* 统计数据 */}
            <div style={{
              padding: '16px',
              borderBottom: '1px solid #e5e7eb'
            }}>
              <Row gutter={[8, 16]}>
                <Col span={12}>
                  <Statistic
                    title="物种总数"
                    value={mapData.totalSpecies}
                    valueStyle={{
                      color: '#1e3a8a',
                      fontSize: '20px',
                      fontWeight: '600'
                    }}
                    suffix="种"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="录音数量"
                    value={mapData.totalRecordings}
                    valueStyle={{
                      color: '#059669',
                      fontSize: '20px',
                      fontWeight: '600'
                    }}
                    suffix="条"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="活跃热点"
                    value={mapData.activeHotspots}
                    valueStyle={{
                      color: '#dc2626',
                      fontSize: '20px',
                      fontWeight: '600'
                    }}
                    suffix="个"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="在线用户"
                    value={mapData.onlineUsers}
                    valueStyle={{
                      color: '#7c3aed',
                      fontSize: '20px',
                      fontWeight: '600'
                    }}
                    suffix="人"
                  />
                </Col>
              </Row>
            </div>

            {/* 图例 */}
            <div style={{
              padding: '16px',
              borderBottom: '1px solid #e5e7eb'
            }}>
              <Title level={5} style={{
                margin: '0 0 12px 0',
                color: '#1f2937',
                fontSize: '14px',
                fontWeight: '600',
                fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
              }}>
                观察频次分布
              </Title>
              <div style={{ fontSize: '12px', color: '#6b7280' }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#dc2626',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>500+</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#ea580c',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>400-500</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#f59e0b',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>300-400</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#eab308',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>250-300</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#84cc16',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>200-250</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#22c55e',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>150-200</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#06b6d4',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>100-150</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#3b82f6',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>50-100</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#8b5cf6',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>15-50</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    background: '#a855f7',
                    marginRight: '8px',
                    borderRadius: '2px'
                  }}></div>
                  <span>0-15</span>
                </div>
              </div>
            </div>

            {/* 快捷操作 */}
            <div style={{
              padding: '16px',
              flex: 1
            }}>
              <Title level={5} style={{
                margin: '0 0 12px 0',
                color: '#1f2937',
                fontSize: '14px',
                fontWeight: '600',
                fontFamily: '"Microsoft YaHei", "SimHei", sans-serif'
              }}>
                快捷操作
              </Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  block
                  size="small"
                  style={{
                    textAlign: 'left',
                    height: 'auto',
                    padding: '8px 12px',
                    borderRadius: '4px'
                  }}
                >
                  查看最新观察记录
                </Button>
                <Button
                  block
                  size="small"
                  style={{
                    textAlign: 'left',
                    height: 'auto',
                    padding: '8px 12px',
                    borderRadius: '4px'
                  }}
                >
                  下载数据报告
                </Button>
                <Button
                  block
                  size="small"
                  style={{
                    textAlign: 'left',
                    height: 'auto',
                    padding: '8px 12px',
                    borderRadius: '4px'
                  }}
                >
                  设置监测提醒
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
