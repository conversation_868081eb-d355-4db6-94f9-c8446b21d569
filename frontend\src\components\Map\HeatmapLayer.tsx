import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';

interface HeatmapPoint {
  lat: number;
  lng: number;
  intensity: number;
  species_count?: number;
  recording_count?: number;
}

interface GridCell {
  bounds: L.LatLngBounds;
  intensity: number;
  count: number;
  points: HeatmapPoint[];
}

interface HeatmapLayerProps {
  data: HeatmapPoint[];
  options?: {
    gridSize?: number;        // 网格大小（度数）- 现在作为基础大小，会根据缩放动态调整
    minOpacity?: number;      // 最小透明度
    maxOpacity?: number;      // 最大透明度
    gradient?: { [key: string]: string };  // 颜色渐变
  };
  onCellClick?: (cell: GridCell) => void;
}

// 防抖函数
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 根据缩放级别计算动态网格大小
const calculateGridSize = (zoom: number, baseGridSize: number = 0.5): number => {
  // 基于 eBird 的观察，实现分层的网格大小策略
  // 调整倍数以获得更合理的宽高比例
  if (zoom <= 4) return baseGridSize * 6.0;    // 大洲级别 - 大网格
  if (zoom <= 6) return baseGridSize * 3.0;    // 国家级别 - 中大网格
  if (zoom <= 8) return baseGridSize * 1.5;    // 地区级别 - 基础网格
  if (zoom <= 10) return baseGridSize * 0.8;   // 城市级别 - 小网格
  if (zoom <= 12) return baseGridSize * 0.4;   // 详细级别 - 很小网格
  return baseGridSize * 0.2;                   // 最精细级别 - 最小网格
};

const HeatmapLayer: React.FC<HeatmapLayerProps> = ({
  data,
  options = {},
  onCellClick,
}) => {
  const map = useMap();
  const [heatLayer, setHeatLayer] = useState<L.LayerGroup | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(map.getZoom());
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  // 使用 ref 来存储防抖函数，避免重复创建
  const debouncedUpdateRef = useRef<((zoom: number) => void) | null>(null);

  // 默认配置
  const defaultOptions = {
    gridSize: 0.3,        // 0.3度网格 - 调整为更合理的基础大小
    minOpacity: 0.2,
    maxOpacity: 0.8,
    gradient: {
      0.0: '#313695',     // 深蓝色 - 无数据
      0.1: '#4575b4',     // 蓝色 - 极低密度
      0.2: '#74add1',     // 浅蓝色 - 低密度
      0.3: '#abd9e9',     // 青色 - 中低密度
      0.4: '#e0f3f8',     // 浅青色 - 中等密度
      0.5: '#ffffbf',     // 黄色 - 中等密度
      0.6: '#fee090',     // 浅橙色 - 中高密度
      0.7: '#fdae61',     // 橙色 - 高密度
      0.8: '#f46d43',     // 深橙色 - 很高密度
      0.9: '#d73027',     // 红色 - 极高密度
      1.0: '#a50026'      // 深红色 - 最高密度
    }
  };

  const config = { ...defaultOptions, ...options };

  // 创建网格并聚合数据 - 现在接受动态网格大小参数
  const createGridCells = useCallback((points: HeatmapPoint[], gridSize: number): GridCell[] => {
    const cellMap = new Map<string, GridCell>();

    points.forEach(point => {
      // 计算网格坐标
      const gridLat = Math.floor(point.lat / gridSize) * gridSize;
      const gridLng = Math.floor(point.lng / gridSize) * gridSize;
      const cellKey = `${gridLat},${gridLng}`;

      // 创建网格边界
      const bounds = L.latLngBounds(
        [gridLat, gridLng],
        [gridLat + gridSize, gridLng + gridSize]
      );

      if (!cellMap.has(cellKey)) {
        cellMap.set(cellKey, {
          bounds,
          intensity: 0,
          count: 0,
          points: []
        });
      }

      const cell = cellMap.get(cellKey)!;
      cell.points.push(point);
      cell.count++;
      cell.intensity += point.intensity;
    });

    // 计算平均强度并归一化
    const cells = Array.from(cellMap.values());
    const maxIntensity = Math.max(...cells.map(cell => cell.intensity));

    cells.forEach(cell => {
      cell.intensity = maxIntensity > 0 ? cell.intensity / maxIntensity : 0;
    });

    return cells;
  }, []); // createGridCells 不依赖任何外部变量

  // 根据强度获取颜色
  const getColorForIntensity = (intensity: number): string => {
    const normalizedIntensity = Math.max(0, Math.min(1, intensity));
    const gradientKeys = Object.keys(config.gradient).map(Number).sort((a, b) => a - b);

    // 找到合适的颜色区间
    for (let i = 0; i < gradientKeys.length - 1; i++) {
      const lower = gradientKeys[i];
      const upper = gradientKeys[i + 1];

      if (normalizedIntensity >= lower && normalizedIntensity <= upper) {
        // 线性插值计算颜色
        const ratio = (normalizedIntensity - lower) / (upper - lower);
        const lowerColor = (config.gradient as any)[lower.toString()];
        const upperColor = (config.gradient as any)[upper.toString()];

        // 简单返回上界颜色（可以实现更复杂的颜色插值）
        return ratio > 0.5 ? upperColor : lowerColor;
      }
    }

    // 默认返回最高强度颜色
    const lastKey = gradientKeys[gradientKeys.length - 1];
    return (config.gradient as any)[lastKey.toString()];
  };

  // 计算当前缩放级别对应的网格大小
  const currentGridSize = useMemo(() => {
    return calculateGridSize(currentZoom, config.gridSize);
  }, [currentZoom, config.gridSize]);

  // 使用 useMemo 缓存网格计算结果，避免不必要的重新计算
  const gridCells = useMemo(() => {
    if (!data || data.length === 0) return [];
    return createGridCells(data, currentGridSize);
  }, [data, currentGridSize, createGridCells]);

  // 渲染热力图层的主要逻辑
  const renderHeatmapLayer = useCallback(() => {
    if (!map || gridCells.length === 0) return;

    // 移除现有的热力图层
    if (heatLayer) {
      map.removeLayer(heatLayer);
    }

    // 创建图层组来管理所有的矩形
    const rectangleGroup = L.layerGroup();

    gridCells.forEach(cell => {
      if (cell.intensity <= 0) return; // 跳过无数据的网格

      // 计算透明度
      const opacity = config.minOpacity + (config.maxOpacity - config.minOpacity) * cell.intensity;

      // 获取颜色
      const color = getColorForIntensity(cell.intensity);

      // 创建矩形
      const rectangle = L.rectangle(cell.bounds, {
        fillColor: color,
        color: color,
        weight: 0,
        opacity: 0,
        fillOpacity: opacity,
      });

      // 添加点击事件
      if (onCellClick) {
        rectangle.on('click', () => {
          onCellClick(cell);
        });
      }

      // 添加悬停效果
      rectangle.on('mouseover', () => {
        rectangle.setStyle({
          weight: 1,
          opacity: 0.8,
          fillOpacity: Math.min(1, opacity + 0.2),
        });
      });

      rectangle.on('mouseout', () => {
        rectangle.setStyle({
          weight: 0,
          opacity: 0,
          fillOpacity: opacity,
        });
      });

      rectangleGroup.addLayer(rectangle);
    });

    // 添加到地图
    rectangleGroup.addTo(map);
    setHeatLayer(rectangleGroup);
  }, [map, gridCells, config, onCellClick, getColorForIntensity, heatLayer]);

  // 主要的渲染效果
  useEffect(() => {
    renderHeatmapLayer();
  }, [renderHeatmapLayer]);

  // 创建防抖的缩放处理函数
  useEffect(() => {
    if (!debouncedUpdateRef.current) {
      debouncedUpdateRef.current = debounce((zoom: number) => {
        setIsUpdating(true);
        setCurrentZoom(zoom);
        // 延迟一点时间来显示更新完成
        setTimeout(() => setIsUpdating(false), 100);
      }, 250); // 250ms 防抖延迟
    }
  }, []);

  // 监听地图缩放事件
  useEffect(() => {
    if (!map) return;

    const handleZoomStart = () => {
      setIsUpdating(true);
    };

    const handleZoomEnd = () => {
      const zoom = map.getZoom();
      if (debouncedUpdateRef.current) {
        debouncedUpdateRef.current(zoom);
      }
    };

    // 绑定事件
    map.on('zoomstart', handleZoomStart);
    map.on('zoomend', handleZoomEnd);

    // 初始化当前缩放级别
    setCurrentZoom(map.getZoom());

    return () => {
      map.off('zoomstart', handleZoomStart);
      map.off('zoomend', handleZoomEnd);
    };
  }, [map]);

  // 组件卸载时的清理逻辑
  useEffect(() => {
    return () => {
      if (heatLayer && map) {
        map.removeLayer(heatLayer);
      }
    };
  }, [heatLayer, map]);

  return null; // 这个组件不渲染任何 DOM 元素
};

export default HeatmapLayer;

// 工具函数：生成模拟热力图数据
export const generateMockHeatmapData = (count: number = 500): HeatmapPoint[] => {
  const data: HeatmapPoint[] = [];
  
  // 定义一些热点区域（模拟真实的海洋生物分布）
  const hotspots = [
    { center: [35.0, 139.0], name: '日本近海' },      // 日本
    { center: [37.0, -122.0], name: '加州海岸' },     // 加州
    { center: [60.0, -150.0], name: '阿拉斯加湾' },   // 阿拉斯加
    { center: [-35.0, 150.0], name: '澳洲东海岸' },   // 澳洲
    { center: [70.0, -8.0], name: '挪威海' },         // 挪威
    { center: [25.0, -80.0], name: '佛罗里达海峡' },  // 佛罗里达
  ];

  hotspots.forEach(hotspot => {
    const pointsInHotspot = Math.floor(count / hotspots.length);
    
    for (let i = 0; i < pointsInHotspot; i++) {
      // 在热点周围生成随机点
      const lat = hotspot.center[0] + (Math.random() - 0.5) * 10;
      const lng = hotspot.center[1] + (Math.random() - 0.5) * 15;
      
      // 距离热点中心越近，强度越高
      const distance = Math.sqrt(
        Math.pow(lat - hotspot.center[0], 2) + 
        Math.pow(lng - hotspot.center[1], 2)
      );
      const intensity = Math.max(0.1, 1 - distance / 8);
      
      data.push({
        lat,
        lng,
        intensity,
        species_count: Math.floor(intensity * 50),
        recording_count: Math.floor(intensity * 200),
      });
    }
  });

  return data;
};
